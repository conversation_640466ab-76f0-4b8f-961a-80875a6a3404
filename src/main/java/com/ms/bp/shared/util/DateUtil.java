package com.ms.bp.shared.util;

import lombok.Setter;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日付処理統一ユーティリティクラス
 * 主な機能：
 * 1. 年度計算処理（次年度取得など）
 * 2. 日付文字列の解析・フォーマット処理
 * 3. システム共通で使用される日付処理の統一化
 * 注意事項：
 * - DateTimeFormatterは本質的にスレッドセーフなため、ThreadLocalは不要
 */
public class DateUtil {

    // ==================== 日付フォーマット定数 ====================


    // テスト用の固定日時（テスト時のみ使用）
    private static volatile String testFixedYear = null;
    /**
     * -- SETTER --
     *  テスト用システム日時を設定（テスト専用）
     * systemDateTime 固定日時文字列（nullで通常モードに戻る）
     */
    // テスト用のシステム日時（テスト時のみ使用）
    @Setter
    private static volatile LocalDateTime testSystemDateTime = null;

    /** 標準日時フォーマット（yyyyMMddHHmmss） */
    private static final DateTimeFormatter STANDARD_DATE_TIME_FORMAT =
            DateTimeFormatter.ofPattern("yyyyMMddHHmmss");

    /** 標準日付フォーマット（yyyy-MM-dd HH:mm:ss） */
    private static final DateTimeFormatter STANDARD_DATE_FORMAT =
            DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    /** 標準日時フォーマット（yyyy/MM/dd） */
    private static final DateTimeFormatter DATE_FORMAT =
            DateTimeFormatter.ofPattern("yyyy/MM/dd");

    /** 標準日時フォーマット（yyyyMMddHHmm） */
    private static final DateTimeFormatter STANDARD_DATE_YYYYMMDDHHMM_FORMAT =
            DateTimeFormatter.ofPattern("yyyyMMddHHmm");

    /** システムデフォルトタイムゾーン */
    private static final ZoneId SYSTEM_ZONE = ZoneId.systemDefault();
    private static final ZoneId UTC_ZONE = ZoneId.of("UTC");
    private static final ZoneId TOKYO_ZONE = ZoneId.of("Asia/Tokyo");
    // ==================== 年度関連メソッド ====================

    /**
     * システム日付から、事業計画システムとしての年度を返却する。
     * ルール
     * システム日付.月　>=　11
     * 		システム日付.年+1 を返却
     * 上記以外
     * 		システム日付.年　を返却
     *
     * @return 計算後の年 (int)
     */
    public static String getNextFiscalYear() {
        // テストモード時は固定値を返す
        if (testFixedYear != null) {
            return testFixedYear;
        }
        LocalDate now = LocalDate.now();
        return String.valueOf(now.getMonthValue() >= 11 ? now.getYear() + 1 : now.getYear());
    }

    /**
     * テスト用固定日時を設定（テスト専用）
     * @param fixedDateTime 固定日時文字列（nullで通常モードに戻る）
     */
    public static void setTestFixedDateTime(String fixedDateTime) {
        testFixedYear = fixedDateTime;
    }

    // ==================== 日付解析メソッド ====================

    /**
     * 日時文字列をDate型に変換（標準フォーマット使用）
     * ImportJobStatusとExportJobStatusのparseDateTime方法と同等の機能を提供
     *
     * @param dateTimeStr 日時文字列（yyyyMMddHHmmss形式）
     * @return 変換されたDate、無効な場合はnull
     */
    public static String parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.isEmpty()) {
            return null;
        }

        try {
            return LocalDateTime.parse(dateTimeStr, STANDARD_DATE_TIME_FORMAT)
                    .atZone(UTC_ZONE)
                    .withZoneSameInstant(TOKYO_ZONE)
                    .format(STANDARD_DATE_FORMAT);
        } catch (Exception e) {
            return null;
        }
    }


    // ==================== 日付フォーマットメソッド ====================

    /**
     * 現在日時を標準フォーマット（yyyyMMddHHmmss）で取得
     *
     * @return 現在日時の文字列
     */
    public static String getCurrentDateTimeString() {
        LocalDateTime now = testSystemDateTime != null ? testSystemDateTime : LocalDateTime.now();
        return now.format(STANDARD_DATE_TIME_FORMAT);
    }

    /**
     * 現在日時を標準フォーマット（yyyyMMddHHmm）で取得
     *
     * @return 現在日時の文字列
     */
    public static String getCurrentDateTimeString_YYYYMMDDHHMM() {
        LocalDateTime now = testSystemDateTime != null ? testSystemDateTime : LocalDateTime.now();
        return now.format(STANDARD_DATE_YYYYMMDDHHMM_FORMAT);
    }

    /**
     * Dateを標準フォーマット（yyyyMMddHHmmss）で文字列に変換
     *
     * @param date 変換するDate
     * @return フォーマットされた日時文字列、nullの場合は空文字列
     */
    public static String formatDateTime(Date date) {
        if (date == null) {
            return "";
        }

        var localDateTime = LocalDateTime.ofInstant(date.toInstant(), SYSTEM_ZONE);
        return localDateTime.format(STANDARD_DATE_TIME_FORMAT);
    }

    /**
     * 現在日時を標準フォーマット（DATE_FORMAT）で取得
     *
     * @return 現在日時の文字列
     */
    public static String getCurrentDateString() {
        LocalDateTime now = testSystemDateTime != null ? testSystemDateTime : LocalDateTime.now();
        return now.format(DATE_FORMAT);
    }
}