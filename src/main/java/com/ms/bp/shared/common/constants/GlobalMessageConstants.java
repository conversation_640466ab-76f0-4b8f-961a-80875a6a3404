package com.ms.bp.shared.common.constants;

import com.ms.bp.shared.common.exception.Message;

/**
 * グローバルエラーコード列挙型
 */
public interface GlobalMessageConstants {

    Message SUCCESS = new Message(0, "成功");

    // ========== クライアントエラーセクション ==========
    Message BAD_REQUEST = new Message(400, "リクエストパラメータが正しくありません");
    Message UNAUTHORIZED = new Message(401, "アカウントはログインしていません");
    Message FORBIDDEN = new Message(403, "操作権限がありません");
    Message NOT_FOUND = new Message(404, "リクエストが見つかりません");


    // ========== ファイル処理エラーセクション ==========
    Message S3_OPERATION_FAILED = new Message(501, "S3操作に失敗しました");

    // ========== 認証・認可エラーセクション ==========
    Message AUTH_TOKEN_EXPIRED = new Message(20001, "認証トークンの期限が切れています");
    Message AUTH_TOKEN_INVALID = new Message(20002, "無効な認証トークンです");
    Message AUTH_FAILED = new Message(20004, "認証に失敗しました");
    Message USER_NOT_FOUND = new Message(20005, "ユーザーが見つかりません");
    Message USER_INACTIVE = new Message(20006, "ユーザーアカウントが無効です");
    Message USER_SUSPENDED = new Message(20007, "ユーザーアカウントが停止されています");

    // ========== システムエラーセクション ==========
    Message INTERNAL_SERVER_ERROR = new Message(50001, "システムエラーが発生しました");
    Message BUSINESS_ERROR = new Message(50005, "業務エラーが発生しました");

    Message ERR_008 = new Message(1008, "エラー内容：登録権限のないエリア情報が入力されています。");
    Message ERR_010 = new Message(1010, "アップロードされたファイルは{0}の形式ではありません。内容をご確認ください。");
    Message ERR_011 = new Message(1011, "CSV作成が正しく行われませんでした。ファイルの種類：{0}、エラー内容：{1}");
    Message ERR_012 = new Message(1012, "条件に一致するデータが取得できませんでした。カラム名：{0}、条件：{1}、エラー内容：{2}");
    Message ERR_013 = new Message(1013, "予期せぬエラーが発生しました。処理名：{0}、エラー内容：{1}");

    // ========== 検証エラーセクション ==========
    Message ERR_015 = new Message(1015, "ファイル種別{0}をアップロードする権限がありません。");
    //必須チェックエラー
    Message ERR_019 = new Message(1019, "パラメータ：{0}、エラー内容：必須チェックエラー");
    Message ERR_016 = new Message(1016, "項目：{0}、エラー内容：必須チェックエラー");
    //桁数チェックエラー
    Message ERR_020 = new Message(1020, "パラメータ：{0}、エラー内容：桁数エラー。{1}桁から{2}桁の間で入力してください。");
    Message ERR_017 = new Message(1017, "項目：{0}、エラー内容：桁数エラー。{1}桁から{2}桁の間で入力してください。");
    //書式チェックエラー
    Message ERR_021 = new Message(1021, "パラメータ：{0}、エラー内容：書式エラー。{1}で入力してください。");
    Message ERR_018 = new Message(1018, "項目：{0}、エラー内容：書式エラー。{1}で入力してください。");
    Message ERR_022 = new Message(1022, "項目：{0}、エラー内容：項目エラー。{1}は存在しないエリアコードです。");
    Message ERR_023 = new Message(1023, "条件に一致するデータが取得できませんでした。");
    Message ERR_027 = new Message(1027, "項目：{0}、エラー内容：登録権限のない値が入力されています。");
    Message ERR_028 = new Message(1028, "ユーザに権限のないエリアが画面指定されました。");
    //処理開始
    Message INF_001 = new Message(2001, "処理開始。{0}");
    Message INF_002 = new Message(2002, "処理終了。{0}　ステータス：{1}");
    Message INF_003 = new Message(2003, "{0}を作成しました。履歴番号：{1}");

}