package com.ms.bp.application.data;

import com.amazonaws.services.lambda.runtime.Context;
import com.ms.bp.application.ExportJobStatusService;
import com.ms.bp.application.ImportJobStatusService;
import com.ms.bp.domain.file.model.ExportJobStatus;
import com.ms.bp.domain.file.model.ImportJobStatus;
import com.ms.bp.domain.master.model.AreaInfo;
import com.ms.bp.domain.user.model.UserInfo;
import com.ms.bp.infrastructure.external.lambda.AsyncLambdaInvoker;
import com.ms.bp.infrastructure.external.s3.S3Service;
import com.ms.bp.interfaces.dto.request.ExportRequest;
import com.ms.bp.interfaces.dto.request.ImportRequest;
import com.ms.bp.interfaces.dto.request.WorkerPayload;
import com.ms.bp.shared.common.constants.BusinessConstants;
import com.ms.bp.util.TestDataManager;
import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import software.amazon.awssdk.core.ResponseInputStream;
import software.amazon.awssdk.http.AbortableInputStream;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

public class PlanMasterImportServiceTest {
    private static final Logger logger = LoggerFactory.getLogger(PlanMasterImportServiceTest.class);

    private DataApplicationService dataApplicationService;

    // Mock オブジェクト
    @Mock
    private AsyncLambdaInvoker mockAsyncLambdaInvoker;

    @Mock
    private S3Service mockS3Service;

    @Mock
    private Context mockLambdaContext;

    private UserInfo testUserInfo;
    private ImportRequest testImportRequest;

    /**
     * ケース毎実施前作業
     */
    @BeforeEach
    void setUp() {
        logger.info("=== importExportTask集成テストセットアップ開始 ===");

        try {
            // Mockito初期化
            MockitoAnnotations.openMocks(this);

            // AWS設定を環境変数で設定してAsyncLambdaInvokerの初期化エラーを回避
            System.setProperty("aws.region", "ap-northeast-1");
            System.setProperty("WORKER_FUNCTION_NAME", "test-worker-function");

            // テスト対象
            dataApplicationService = new DataApplicationService();

            // DataApplicationService内のAsyncLambdaInvokerをモックに置き換え
            // リフレクションを使用してプライベートフィールドにアクセス
            Field asyncLambdaInvokerField = DataApplicationService.class.getDeclaredField("asyncLambdaInvoker");
            asyncLambdaInvokerField.setAccessible(true);
            asyncLambdaInvokerField.set(dataApplicationService, mockAsyncLambdaInvoker);

            // S3Serviceもモックに置き換え（FileProcessingServiceとTaskOrchestrationService内のS3Service）
            // DataApplicationService内のTaskOrchestrationServiceのS3Serviceを置き換える必要がある
            Field taskOrchestrationServiceField = DataApplicationService.class.getDeclaredField("taskOrchestrationService");
            taskOrchestrationServiceField.setAccessible(true);
            Object taskOrchestrationService = taskOrchestrationServiceField.get(dataApplicationService);

            // TaskOrchestrationService内のFileExportOrchestratorを取得
            Field fileImportOrchestratorField = taskOrchestrationService.getClass().getDeclaredField("fileImportOrchestrator");
            fileImportOrchestratorField.setAccessible(true);
            Object fileImportOrchestrator = fileImportOrchestratorField.get(taskOrchestrationService);

            // FileExportOrchestrator内のFileProcessingServiceを取得
            Field fileProcessingServiceField = fileImportOrchestrator.getClass().getDeclaredField("fileProcessingService");
            fileProcessingServiceField.setAccessible(true);
            Object fileProcessingService = fileProcessingServiceField.get(fileImportOrchestrator);

            // FileProcessingService内のS3Serviceを置き換え
            Field s3ServiceField = fileProcessingService.getClass().getDeclaredField("s3Service");
            s3ServiceField.setAccessible(true);
            s3ServiceField.set(fileProcessingService, mockS3Service);

            // Mock の基本設定
            setupMockBehaviors();

            logger.info("=== importExportTask集成テストセットアップ完了 ===");

        } catch (Exception e) {
            logger.error("テストセットアップエラー: {}", e.getMessage(), e);
            throw new RuntimeException("テストセットアップに失敗しました", e);
        }

        // テスト用ユーザー情報を作成
        testUserInfo = createTestUserInfo();

        // テスト用インポートリクエストを作成
        testImportRequest = createTestImportRequest();
    }

    @Test
    void testExecuteImportTask_ヘッダ数不正_異常系(){
        String uploadFilePath = "planmasterimport/uploaddata/planmaster_import_test_data_1.csv";
        testImportRequest.setS3Key(uploadFilePath);
        setupMockS3(uploadFilePath);

        // 実行と実行前後の履歴確認
        doExecute(BusinessConstants.BATCH_STATUS_COMPLETED_CODE);


    }





    // ==================== プライベートメソッド ====================
    /**
     * Mock オブジェクトの基本動作を設定
     */
    private void setupMockBehaviors() {
        try {
            // AsyncLambdaInvoker の Mock 設定
            doNothing().when(mockAsyncLambdaInvoker).invokeAsync(any(WorkerPayload.class));

            // S3Service の Mock 設定
            when(mockS3Service.uploadFileFromStream(any(), anyString(), anyString(), anyLong(), any()))
                    .thenReturn(Map.of("success", true, "key", "import-errors/error.csv"));

            // Lambda Context の Mock 設定
            when(mockLambdaContext.getRemainingTimeInMillis()).thenReturn(900000); // 15分（BackgroundTimeoutMonitorの緩衝時間10分より大きく設定）
            when(mockLambdaContext.getAwsRequestId()).thenReturn("test-request-id");

            logger.debug("Mock オブジェクトの基本動作設定完了");
        } catch (Exception e) {
            logger.error("Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException("Mock 設定に失敗しました", e);
        }
    }

    /**
     * 動態的の S3Service の Mock 設定
     */
    private void setupMockS3(String filePath) {
        try {
            InputStream inputStream = getClass().getClassLoader().getResourceAsStream("testdata/" + filePath);
            GetObjectResponse getObjectResponse = GetObjectResponse.builder().build();
            ResponseInputStream<GetObjectResponse> responseInputStream = new ResponseInputStream<>(
                    getObjectResponse,
                    AbortableInputStream.create(inputStream)
            );
            when(mockS3Service.getInputStreamFromS3Url(eq(filePath))).thenReturn(responseInputStream);
        } catch (IOException e) {
            logger.error("S3 Mock 設定エラー: {}", e.getMessage(), e);
            throw new RuntimeException(e);
        }
    }

    /**
     * ImportJobStatusを追加と内容検証と情報を取得
     * 状態表データの検証用
     */
    private Pair<Long,Integer> addAndCheckAndGetImportJobInfo() {
        // 事前にjobを手動追加する
        var importJobResponse = dataApplicationService.startImport(createTestImportRequest(),
                createTestUserInfo(), mockLambdaContext);
        Long rrkBango = Long.parseLong(importJobResponse.getJobId());
        // ジョブが正常に作成されたことを確認
        assertNotNull(importJobResponse.getJobId());
        assertEquals("ACCEPTED", importJobResponse.getStatus());

        // 初期状態の確認
        ImportJobStatus initialStatus = getImportJobStatusFromDatabase(rrkBango);
        assertNotNull(initialStatus);
        assertEquals(BusinessConstants.BATCH_STATUS_PROCESSING_CODE, initialStatus.getStts());

        return  Pair.of(rrkBango,initialStatus.getVrsn());
    }

    /**
     * 指定番号でステータスを比較する
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     * @param oldVrsn 実施前バージョン
     * @param status ステータス
     */
    private void checkImportJobInfo(Long rrkBango,Integer oldVrsn,String status) {
        ImportJobStatus finalStatus = getImportJobStatusFromDatabase(rrkBango);
        assertNotNull(finalStatus);
        assertEquals(status, finalStatus.getStts());

        // バージョンが更新されていることを確認
        assertTrue(finalStatus.getVrsn() > oldVrsn);
        TestDataManager.deleteTableData("T_UPLOAD_RRK", List.of(Map.of( "RRK_BANGO", rrkBango)));
    }

    /**
     * データベースからImportJobStatusを取得
     * 状態表データの検証用
     *
     * @param rrkBango 履歴番号
     */
    private ImportJobStatus getImportJobStatusFromDatabase(Long rrkBango) {
        try {
            // 真実のImportJobStatusServiceインスタンスを使用してデータベースから取得
            ImportJobStatusService importJobStatusService = new ImportJobStatusService();
            return importJobStatusService.getJobStatus(rrkBango);
        } catch (Exception e) {
            logger.error("ImportJobStatus取得エラー: rrkBango={}, error={}", rrkBango, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 実行と実行前後の履歴確認
     *
     * @param status 履歴ステータス
     */
    private void doExecute(String status) {
        // 実施前ダウンロード履歴jobを手動追加と内容検証
        var initialStatus = addAndCheckAndGetImportJobInfo();

        // テスト用WorkerPayloadを作成
        WorkerPayload payload=WorkerPayload.builder()
                .jobId(initialStatus.getLeft().toString())
                .operationType(BusinessConstants.OPERATION_UPLOAD_CODE)
                .request(testImportRequest)
                .userInfo(testUserInfo)
                .build();

        // executeExportTask を実行
        assertDoesNotThrow(() -> {
            dataApplicationService.executeImportTask(payload, mockLambdaContext);
        });

        // 実施後アップロード履歴jobの状態を検討
        checkImportJobInfo(initialStatus.getLeft(),initialStatus.getRight(),status);
    }

    /**
     * テスト用インポートリクエストを作成
     */
    private ImportRequest createTestImportRequest() {
        ImportRequest importRequest = new ImportRequest();
        importRequest.setS3Key("");
        importRequest.setDataType(BusinessConstants.FILE_TYPE_PLAN_MASTER_CODE);
        importRequest.setArea(Arrays.asList("0000"));
        return importRequest;
    }

    /**
     * テスト用ユーザー情報を作成
     */
    private UserInfo createTestUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setShainCode("JNED01");
        userInfo.setSystemOperationCompanyCode("100001");
        userInfo.setUnitCode("11300");
        AreaInfo area = new AreaInfo("0000", "テスト用_本社");
        AreaInfo area1 = new AreaInfo("0100", "テスト用_職能部門");
        AreaInfo area2 = new AreaInfo("0200", "テスト用_");
        userInfo.setAreaInfos(List.of(area, area1, area2));
        userInfo.setAreaCode("0000");
        userInfo.setAreaName("テスト用_本社");
        userInfo.setPositionCode("99");
        userInfo.setGroupCode("0099");
        userInfo.setPositionSpecialCheck("1");
        return userInfo;
    }
}